import type { AccordionItemData } from "@/types";

export const getLocalizedFaqData = (
  t: (key: string) => string,
): AccordionItemData[] => [
  {
    value: "item-1",
    title: t("faq.items.item1.title"),
    content: (
      <>
        <p>{t("faq.items.item1.content.paragraph1")}</p>
        <p>{t("faq.items.item1.content.paragraph2")}</p>
        <p>{t("faq.items.item1.content.paragraph3")}</p>
      </>
    ),
  },
  {
    value: "item-2",
    title: t("faq.items.item2.title"),
    content: (
      <>
        <p>{t("faq.items.item2.content.paragraph1")}</p>
        <p>{t("faq.items.item2.content.paragraph2")}</p>
      </>
    ),
  },
  {
    value: "item-3",
    title: t("faq.items.item3.title"),
    content: <p>{t("faq.items.item3.content")}</p>,
  },
  {
    value: "item-4",
    title: t("faq.items.item4.title"),
    content: <p>{t("faq.items.item4.content")}</p>,
  },
  {
    value: "item-5",
    title: t("faq.items.item5.title"),
    content: <p>{t("faq.items.item5.content")}</p>,
  },
];
