import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { PhoneInput } from "@/components/utils/phone-input";
import useContactUsForm from "@/hooks/use-contact-us-form";
import { useTranslation } from "react-i18next";

export default function ContactUsForm() {
  const { form, onSubmit } = useContactUsForm();
  const { t } = useTranslation();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-10">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="f_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  {t("contactUs.form.firstName")}
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("contactUs.form.firstNamePlaceholder")}
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="l_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  {t("contactUs.form.lastName")}
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("contactUs.form.lastNamePlaceholder")}
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  {t("contactUs.form.email")}
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("contactUs.form.emailPlaceholder")}
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  {t("contactUs.form.phone")}
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <PhoneInput
                    dir="rtl"
                    placeholder={t("contactUs.form.phonePlaceholder")}
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                    defaultCountry="SA"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                {t("contactUs.form.message")}
              </FormLabel>
              <FormControl>
                <Textarea
                  dir="rtl"
                  placeholder={t("contactUs.form.messagePlaceholder")}
                  className="h-[120px] bg-white shadow-xs"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="h-[40px] cursor-pointer !bg-[#035859]">
          {t("contactUs.form.submit")}
        </Button>
      </form>
    </Form>
  );
}
