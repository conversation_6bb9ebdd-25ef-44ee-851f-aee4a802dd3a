import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import ServicesList from "@/components/services/services-list";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export default function ServicesPage() {
  const { t } = useTranslation();
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: t("breadcrumbs.home"), href: URLS.home },
          { label: t("breadcrumbs.services"), isCurrent: true },
        ]}
      />

      <Banner
        title={t("services.title")}
        description={t("services.description")}
      />

      <ServicesList />
    </>
  );
}
