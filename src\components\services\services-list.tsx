import { getLocalizedServicesList } from "@/data/localized-services-list";
import { useTranslation } from "react-i18next";
import ServicesItem from "./services-item";

export default function ServicesList() {
  const { t } = useTranslation();
  const servicesList = getLocalizedServicesList(t);
  return (
    <div className="container grid grid-cols-1 gap-5 px-20 py-10 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {servicesList.map((service) => (
        <ServicesItem key={service.id} {...service} />
      ))}
    </div>
  );
}
