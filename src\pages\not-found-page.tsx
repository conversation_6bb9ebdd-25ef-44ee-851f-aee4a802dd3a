import { Button } from "@/components/ui/button";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";

export default function NotFoundPage() {
  const { t } = useTranslation();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-4 text-center">
      <h1 className="text-4xl font-bold">{t("errors.notFound")}</h1>
      <p className="text-lg text-gray-600">{t("errors.notFoundDescription")}</p>
      <Link to={URLS.home}>
        <Button>{t("errors.goHome")}</Button>
      </Link>
    </div>
  );
}
