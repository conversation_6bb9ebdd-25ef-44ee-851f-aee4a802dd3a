import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { languages, type LanguageCode } from "@/localization/i18n";
import { Globe } from "lucide-react";
import { useTranslation } from "react-i18next";

interface LanguageSwitcherProps {
  variant?: "default" | "outline" | "ghost";
  className?: string;
}

export default function LanguageSwitcher({ 
  variant = "outline", 
  className = "" 
}: LanguageSwitcherProps) {
  const { i18n, t } = useTranslation();
  const currentLang = languages[i18n.language as LanguageCode];

  const handleLanguageChange = (langCode: LanguageCode) => {
    i18n.changeLanguage(langCode);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          className={`cursor-pointer border-white !bg-transparent !text-white transition-colors hover:bg-white hover:text-[#002C2D] ${className}`}
        >
          {t("nav.language")}
          <Globe className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[120px]">
        {Object.values(languages).map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            className={`cursor-pointer ${
              currentLang?.code === lang.code ? "bg-gray-100" : ""
            }`}
          >
            <span className="mr-2">{lang.flag}</span>
            {lang.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
