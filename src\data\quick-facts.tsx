import type { AccordionItemData } from "@/types";

export const getLocalizedQuickFactsData = (
  t: (key: string) => string,
): AccordionItemData[] => [
  {
    value: "fact-1",
    title: t("quickFacts.items.fact1.title"),
    content: <p>{t("quickFacts.items.fact1.content")}</p>,
  },
  {
    value: "fact-2",
    title: t("quickFacts.items.fact2.title"),
    content: <p>{t("quickFacts.items.fact2.content")}</p>,
  },
  {
    value: "fact-3",
    title: t("quickFacts.items.fact3.title"),
    content: <p>{t("quickFacts.items.fact3.content")}</p>,
  },
  {
    value: "fact-4",
    title: t("quickFacts.items.fact4.title"),
    content: <p>{t("quickFacts.items.fact4.content")}</p>,
  },
  {
    value: "fact-5",
    title: t("quickFacts.items.fact5.title"),
    content: <p>{t("quickFacts.items.fact5.content")}</p>,
  },
];
