import hero_image from "@/assets/images/hero.jpg";
import { useTranslation } from "react-i18next";

export default function AboutHero() {
  const { t } = useTranslation();
  return (
    <div
      style={{ backgroundImage: `url(${hero_image})` }}
      className="relative flex h-screen rotate-y-180 items-center bg-cover bg-center bg-no-repeat"
    >
      <div className="absolute inset-0 bg-gradient-to-b from-[#000]/10 to-[#02302C]/90"></div>
      <div className="container flex rotate-y-180 flex-col gap-4 px-4 text-center text-white sm:gap-5 sm:px-6 md:px-8 lg:px-12 xl:px-20">
        <h1 className="text-3xl font-[600] sm:text-4xl md:text-5xl lg:text-6xl xl:text-[72px]">
          {t("about.title")}
        </h1>
        <p className="text-sm leading-6 font-[400] sm:text-base sm:leading-7 md:text-lg md:leading-8 lg:text-[20px]">
          {t("about.description")}
        </p>
      </div>
    </div>
  );
}
