import type { NavLink } from "@/types";
import { URLS } from "@/utils/urls";

export const getLocalizedNavLinks = (t: (key: string) => string): NavLink[] => [
  {
    id: 1,
    text: t("nav.home"),
    link: URLS.home,
  },
  {
    id: 2,
    text: t("nav.about"),
    link: URLS.about,
  },
  {
    id: 3,
    text: t("nav.services"),
    link: URLS.services,
  },
  {
    id: 4,
    text: t("nav.documents"),
    link: URLS.documentations,
  },
  {
    id: 5,
    text: t("nav.quickFacts"),
    link: URLS.quickFacts,
  },
  {
    id: 6,
    text: t("nav.faq"),
    link: URLS.faq,
  },
  {
    id: 7,
    text: t("nav.contactUs"),
    link: URLS.contactUs,
  },
];
