import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { arLocales } from "./ar-locales";
import { enLocales } from "./en-locales";

const resources = {
  en: {
    translation: enLocales,
  },
  ar: {
    translation: arLocales,
  },
};

// Language configuration
export const languages = {
  en: {
    code: "en",
    name: "English",
    dir: "ltr",
    flag: "🇺🇸",
  },
  ar: {
    code: "ar",
    name: "العربية",
    dir: "rtl",
    flag: "🇸🇦",
  },
} as const;

export type LanguageCode = keyof typeof languages;

// Get saved language or default to Arabic
const getInitialLanguage = (): LanguageCode => {
  const saved = localStorage.getItem("gdf-locale") as LanguageCode;
  return saved && saved in languages ? saved : "ar";
};

// Update document direction and language
export const updateDocumentLanguage = (langCode: LanguageCode) => {
  const lang = languages[langCode];
  document.documentElement.lang = lang.code;
  document.documentElement.dir = lang.dir;
  document.body.dir = lang.dir;
};

i18n.use(initReactI18next).init({
  resources,
  lng: getInitialLanguage(),
  fallbackLng: "ar",
  debug: process.env.NODE_ENV === "development",

  interpolation: {
    escapeValue: false,
  },

  react: {
    useSuspense: false,
  },
});

// Update document language on initialization
updateDocumentLanguage(i18n.language as LanguageCode);

// Listen for language changes
i18n.on("languageChanged", (lng: string) => {
  updateDocumentLanguage(lng as LanguageCode);
  localStorage.setItem("gdf-locale", lng);
});

export default i18n;
