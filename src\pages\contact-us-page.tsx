import { URLS } from "@/utils/urls";

import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import ContactUsData from "@/components/contact-us/contact-us-data";
import ContactUsForm from "@/components/contact-us/contact-us-form";
import ContactUsHeading from "@/components/contact-us/contact-us-heading";
import { useTranslation } from "react-i18next";

export default function ContactUsPage() {
  const { t } = useTranslation();
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: t("breadcrumbs.home"), href: URLS.home },
          { label: t("breadcrumbs.contactUs"), isCurrent: true },
        ]}
      />

      <div className="container grid grid-cols-1 gap-10 pb-10 lg:grid-cols-3 lg:pb-0">
        <div className="lg:col-span-2">
          <ContactUsHeading />
          <ContactUsForm />
        </div>
        <ContactUsData />
      </div>
    </>
  );
}
