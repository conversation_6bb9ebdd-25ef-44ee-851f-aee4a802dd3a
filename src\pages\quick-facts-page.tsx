import { AccordionList } from "@/components/common/accordion-list";
import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import { quickFactsData } from "@/data/quick-facts";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export default function QuickFactsPage() {
  const { t } = useTranslation();
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: t("breadcrumbs.home"), href: URLS.home },
          { label: t("breadcrumbs.quickFacts"), isCurrent: true },
        ]}
      />

      <Banner
        title={t("quickFacts.title")}
        description={t("quickFacts.description")}
      />

      <AccordionList items={quickFactsData} />
    </>
  );
}
