import { Co<PERSON>, Mail, MapPin, Phone } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function ContactUsData() {
  const { t } = useTranslation();
  return (
    <div className="flex h-fit flex-col gap-5 rounded-[16px] bg-white p-5 shadow">
      <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[24px] font-[600] text-transparent">
        {t("contactUs.title")}
      </h2>

      <div className="flex items-start gap-2">
        <Phone color="#17663A" />
        <div>
          <span className="text-[16px] font-[700] text-[#17663A]">
            {t("common.phone")}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-[16px] font-[400] text-[#121217]">
              9200343222
            </span>
            <Copy className="cursor-pointer" size={20} />
          </div>
        </div>
      </div>
      <div className="flex items-start gap-2">
        <Mail color="#17663A" />
        <div>
          <span className="text-[16px] font-[700] text-[#17663A]">
            {t("common.email")}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-[16px] font-[400] text-[#121217]">
              <EMAIL>
            </span>
            <Copy className="cursor-pointer" size={20} />
          </div>
        </div>
      </div>
      <div className="flex items-start gap-2">
        <Mail color="#17663A" />
        <div>
          <span className="text-[16px] font-[700] text-[#17663A]">فاكس</span>
          <div className="flex items-center gap-2">
            <span className="text-[16px] font-[400] text-[#121217]">
              00966-11-434-6654
            </span>
            <Copy className="cursor-pointer" size={20} />
          </div>
        </div>
      </div>
      <div className="flex items-start gap-2">
        <MapPin color="#17663A" />
        <div>
          <span className="text-[16px] font-[700] text-[#17663A]">
            {t("common.address")}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-[16px] font-[400] text-[#121217]">
              Riyadh
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
