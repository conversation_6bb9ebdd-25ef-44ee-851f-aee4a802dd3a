import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import { Button } from "@/components/ui/button";
import { getLocalizedServicesList } from "@/data/localized-services-list";
import { URLS } from "@/utils/urls";
import { CircleUser, Computer, Link2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link, useParams } from "react-router";

export default function ServiceDetailsPage() {
  const { serviceId } = useParams();
  const { t } = useTranslation();
  const servicesList = getLocalizedServicesList(t);

  const service = servicesList.find(
    (service) => service.id === Number(serviceId),
  );

  if (!service) {
    return <div>{t("services.serviceNotFound")}</div>;
  }

  return (
    <>
      <AppBreadcrumb
        items={[
          { label: t("breadcrumbs.home"), href: URLS.home },
          { label: t("breadcrumbs.services"), href: URLS.services },
          {
            label: service.title,
            isCurrent: true,
          },
        ]}
      />

      <div className="container grid grid-cols-1 gap-10 pb-20 lg:grid-cols-3">
        <div className="flex flex-col gap-10 lg:col-span-2">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[30px] font-[700] text-transparent">
                {service.title}
              </h2>
              <Link to={"#"}>
                <Button className="cursor-pointer !bg-[#035859] !text-white">
                  {t("common.startNow")}
                </Button>
              </Link>
            </div>
            <p className="text-[16px] font-[400] text-[#121217]">
              {service.details.description}
            </p>
            <Link
              to={"#"}
              className="flex items-center gap-2 bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-transparent"
            >
              <Link2 className="text-[#599379]" />
              {t("services.usageGuide")}
            </Link>
          </div>

          {service.details.steps && (
            <div className="flex flex-col gap-5">
              <div className="w-fit bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[20px] font-[700] text-transparent">
                {t("services.steps")}
                <span className="mt-2 block h-[3px] w-full rounded-full bg-gradient-to-r from-[#035859] to-[#599379]"></span>
              </div>
              <div className="flex flex-col gap-3">
                {service.details.steps.map((element, index) => {
                  return <div key={index}>{element}</div>;
                })}
              </div>
            </div>
          )}
        </div>
        {/*  */}
        <div className="flex h-fit flex-col gap-5 rounded-[16px] bg-white p-5 shadow">
          <div className="flex gap-2">
            <CircleUser size={24} className="shrink-0" color="#599379" />
            <div className="flex flex-col gap-2">
              <div className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[16px] font-[700] text-transparent">
                {t("services.beneficiaries")}
              </div>
              <p className="text-[16px] font-[400] text-[#121217]">
                {service.details.beneficiaries}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Computer size={24} className="shrink-0" color="#599379" />
            <div className="flex flex-col gap-2">
              <div className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[16px] font-[700] text-transparent">
                {t("services.termsAndConditions")}
              </div>
              <p className="text-[16px] font-[400] text-[#121217]">
                {service.details.conditions}
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
