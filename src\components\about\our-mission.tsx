import hero_image from "@/assets/images/hero.jpg";
import mission_image from "@/assets/images/mission.jpg";
import { useTranslation } from "react-i18next";
import Slider from "react-slick";

const mission_settings = {
  dots: false,
  infinite: false,
  speed: 800,
  autoplay: true,
  autoplaySpeed: 4000,
  slidesToShow: 1.5,
  slidesToScroll: 1.5,
  initialSlide: 0,
  rtl: true,
  arrows: false,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 1.5,
        slidesToScroll: 1.5,
        infinite: true,
        dots: true,
      },
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ],
};

export default function OurMission() {
  const { t } = useTranslation();
  return (
    <div className="relative">
      <img
        src={hero_image}
        alt=""
        className="absolute -z-1 h-full w-full object-cover"
      />

      <div className="absolute inset-0 -z-1 bg-gradient-to-b from-[#fff] to-[#000]/50" />

      <div className="container py-20">
        <h1 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-center text-[48px] font-[600] text-transparent">
          {t("about.mission.title")}
        </h1>

        <Slider {...mission_settings} className="mt-10">
          {[...new Array(4)].map((_, index) => {
            return (
              <div className="px-2" key={index}>
                <div
                  className="relative flex h-[500px] items-center justify-end rounded-lg bg-cover bg-center p-10 text-end text-white"
                  style={{
                    backgroundImage: `url(${mission_image})`,
                  }}
                >
                  <div className="absolute inset-0 rounded-lg bg-[#2E735F]/30"></div>
                  <div className="relative flex w-[500px] flex-col gap-3">
                    <h2 className="text-[48px] font-[400]">
                      {t("about.mission.slides.slide1.title")}
                    </h2>
                    <div className="flex flex-col gap-1">
                      {(
                        t("about.mission.slides.slide1.points", {
                          returnObjects: true,
                        }) as string[]
                      ).map((point: string, pointIndex: number) => (
                        <p key={pointIndex}>. {point}</p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>
      </div>
    </div>
  );
}
