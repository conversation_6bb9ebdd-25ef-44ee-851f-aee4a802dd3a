import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useContactUsForm() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    f_name: z.string().nonempty(t("contactUs.validation.firstNameRequired")),
    l_name: z.string().nonempty(t("contactUs.validation.lastNameRequired")),
    email: z.string().email(t("contactUs.validation.emailInvalid")),
    phone: z.string().min(10, t("contactUs.validation.phoneInvalid")),
    message: z.string().optional(),
  });
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      f_name: "",
      l_name: "",
      email: "",
      phone: "",
      message: "",
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    console.log(data);
  }

  return {
    form,
    onSubmit,
  };
}
