import { AccordionList } from "@/components/common/accordion-list";
import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import { getLocalizedFaqData } from "@/data/faq";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export default function FAQPage() {
  const { t } = useTranslation();
  const faqData = getLocalizedFaqData(t);

  return (
    <>
      <AppBreadcrumb
        items={[
          { label: t("breadcrumbs.home"), href: URLS.home },
          { label: t("breadcrumbs.faq"), isCurrent: true },
        ]}
      />

      <Banner title={t("faq.title")} description={t("faq.description")} />

      <AccordionList items={faqData} />
    </>
  );
}
