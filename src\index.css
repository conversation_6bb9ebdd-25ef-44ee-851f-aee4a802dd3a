@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  /* Breakpoints  */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  .container {
    @apply mx-auto px-4;
  }
  .container-fluid {
    @apply w-full px-4;
  }
}

body {
  font-family: "IBM Plex Sans Arabic", sans-serif;
}

::selection {
  @apply text-primary-foreground bg-[#035859];
}

/* Responsive font utilities */
@layer utilities {
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }
  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }
  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl;
  }
  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl;
  }

  /* Responsive spacing utilities */
  .p-responsive {
    @apply p-4 sm:p-6 md:p-8 lg:p-10;
  }
  .px-responsive {
    @apply px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20;
  }
  .py-responsive {
    @apply py-6 sm:py-8 md:py-10 lg:py-12;
  }

  /* Responsive gap utilities */
  .gap-responsive {
    @apply gap-3 sm:gap-4 md:gap-5 lg:gap-6;
  }
  .gap-responsive-sm {
    @apply gap-2 sm:gap-3 md:gap-4;
  }
}

.slick-dots li.slick-active div {
  @apply h-3 w-7 bg-[#035859];
}

/* make slick track + slides take full height */
.slick-track {
  display: flex !important;
}

.slick-slide {
  height: auto !important; /* reset inline height */
}

.slick-slide > div {
  height: 100%;
}

/* Responsive video improvements */
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Prevent horizontal scroll on mobile */
body {
  overflow-x: hidden;
}

/* Better text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced carousel transitions */
.embla__container {
  transition-timing-function: linear;
}

/* Smooth carousel slide transitions - optimized for ultra fast speed */
[data-slot="carousel-content"] {
  transition: transform 0.1s linear;
}

/* Optimize wheel scrolling performance */
[data-slot="carousel"] {
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Smooth wheel dragging state */
.is-wheel-dragging [data-slot="carousel-content"] {
  transition: none; /* Disable transitions during wheel dragging for immediate response */
}

/* Optimize individual slides for better performance */
[data-slot="carousel-item"] {
  will-change: transform;
  transform: translateZ(0);
}

/* Home slider navbar overlay styles */
.home-slider-navbar nav {
  background: rgba(0, 44, 45, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure bullets are above navbar */
.home-slider-bullets {
  z-index: 250;
}

/* Home slider sections - ensure proper spacing for navbar */
/* .home-slider [data-slot="carousel-item"] {
  padding-top: 0 !important;
  box-sizing: border-box;
} */

/* Adjust Hero section content positioning to account for navbar */
/* .home-slider section .absolute.top-1\/2.left-1\/2 {
  top: calc(
    50% + 1.25rem
  );
} */

/* For sections with h-screen that have padding wrapper, adjust height */
/* .home-slider
  [data-slot="carousel-item"]
  > div[style*="paddingTop"]
  section.h-screen {
  height: calc(100vh - 5rem);
} */

/* ExplorerApp section adjustment - it doesn't use h-screen class */
/* .home-slider
  [data-slot="carousel-item"]
  > div[style*="paddingTop"]
  section:not(.h-screen) {
  min-height: calc(100vh - 5rem);
} */
